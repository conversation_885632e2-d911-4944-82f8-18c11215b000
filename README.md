# 🤖 Microchip AI Chatbot

A modern React TypeScript chatbot application that integrates with the Microchip AI API to provide assistance with microcontrollers, development tools, and products.

## ✨ Features

- **Secure API Key Input**: Safe input with visibility toggle and connection testing
- **Real-time Chat Interface**: Modern chat UI with typing indicators and message timestamps
- **Error Handling**: Comprehensive error handling with user-friendly messages
- **Responsive Design**: Works seamlessly on desktop and mobile devices
- **Conversation History**: Maintains context for better AI responses
- **Modern UI**: Beautiful gradient design with smooth animations

## 🚀 Getting Started

### Prerequisites

- Node.js (version 16 or higher)
- npm or yarn
- A valid Microchip AI API key

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd newAIScreen
```

2. Install dependencies:
```bash
npm install
```

3. Start the development server:
```bash
npm run dev
```

4. Open your browser and navigate to `http://localhost:5173`

### Building for Production

```bash
npm run build
```

The built files will be in the `dist` directory.

## 🔧 Usage

1. **Enter API Key**: When you first open the application, you'll be prompted to enter your Microchip AI API key
2. **Test Connection**: The app will automatically test the connection to ensure your API key is valid
3. **Start Chatting**: Once connected, you can start asking questions about microcontrollers, development tools, and more
4. **Manage Chat**: Use the "Clear" button to reset the conversation or "Change Key" to use a different API key

## 🏗️ Project Structure

```
src/
├── components/
│   ├── ApiKeyInput.tsx     # API key input component
│   ├── ApiKeyInput.css     # Styling for API key input
│   ├── Chatbot.tsx         # Main chatbot interface
│   └── Chatbot.css         # Styling for chatbot
├── services/
│   └── MicrochipAPI.ts     # API service class
├── App.tsx                 # Main application component
├── App.css                 # Application styling
├── index.css               # Global styles
└── main.tsx               # Application entry point
```

## 🔑 API Integration

The application uses the Microchip AI API with the following endpoint:
- **Base URL**: `https://ai-apps.microchip.com`
- **Endpoint**: `/CodeGPTAPI/api/Chat/CodeCompletion`
- **Method**: POST
- **Authentication**: API key in headers

## 🛠️ Technologies Used

- **React 18**: Modern React with hooks
- **TypeScript**: Type-safe development
- **Vite**: Fast build tool and development server
- **CSS3**: Modern styling with animations and gradients
- **Fetch API**: For HTTP requests to the Microchip API

## 📱 Features in Detail

### API Key Management
- Secure input with show/hide toggle
- Real-time validation
- Connection testing before enabling chat

### Chat Interface
- Real-time messaging
- Typing indicators
- Message timestamps
- Auto-scroll to latest messages
- Error handling with retry options

### Responsive Design
- Mobile-friendly interface
- Adaptive layouts
- Touch-friendly controls

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🆘 Support

For issues related to the Microchip AI API, please contact Microchip support.
For application issues, please create an issue in this repository.
