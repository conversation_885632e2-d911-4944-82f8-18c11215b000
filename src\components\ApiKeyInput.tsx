import React, { useState } from 'react';
import { microchipAPI } from '../services/MicrochipAPI';
import './ApiKeyInput.css';

interface ApiKeyInputProps {
  onApiKeySet: (isValid: boolean) => void;
}

export const ApiKeyInput: React.FC<ApiKeyInputProps> = ({ onApiKeySet }) => {
  const [apiKey, setApiKey] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [showKey, setShowKey] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!apiKey.trim()) {
      setError('Please enter an API key');
      return;
    }

    setIsLoading(true);
    setError('');

    try {
      microchipAPI.setApiKey(apiKey);
      const result = await microchipAPI.testConnection();
      
      if (result.success) {
        onApiKeySet(true);
      } else {
        setError(result.error || 'Failed to connect to API');
        onApiKeySet(false);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unexpected error occurred');
      onApiKeySet(false);
    } finally {
      setIsLoading(false);
    }
  };

  const handleKeyChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setApiKey(e.target.value);
    if (error) setError(''); // Clear error when user starts typing
  };

  return (
    <div className="api-key-container">
      <div className="api-key-card">
        <div className="header">
          <h1>🤖 Microchip AI Chatbot</h1>
          <p>Enter your API key to start chatting</p>
        </div>

        <form onSubmit={handleSubmit} className="api-key-form">
          <div className="input-group">
            <label htmlFor="apiKey">API Key</label>
            <div className="input-wrapper">
              <input
                id="apiKey"
                type={showKey ? 'text' : 'password'}
                value={apiKey}
                onChange={handleKeyChange}
                placeholder="Enter your Microchip API key"
                disabled={isLoading}
                className={error ? 'error' : ''}
              />
              <button
                type="button"
                className="toggle-visibility"
                onClick={() => setShowKey(!showKey)}
                disabled={isLoading}
                aria-label={showKey ? 'Hide API key' : 'Show API key'}
              >
                {showKey ? '👁️' : '👁️‍🗨️'}
              </button>
            </div>
          </div>

          {error && (
            <div className="error-message">
              <span className="error-icon">⚠️</span>
              {error}
            </div>
          )}

          <button
            type="submit"
            disabled={isLoading || !apiKey.trim()}
            className="connect-button"
          >
            {isLoading ? (
              <>
                <span className="spinner"></span>
                Testing Connection...
              </>
            ) : (
              <>
                🔗 Connect
              </>
            )}
          </button>
        </form>

        <div className="info-section">
          <h3>ℹ️ About</h3>
          <p>
            This chatbot connects to the Microchip AI API to help you with questions about 
            microcontrollers, development tools, and products.
          </p>
          <ul>
            <li>Your API key is stored locally and never shared</li>
            <li>Conversations are sent to Microchip's servers for processing</li>
            <li>Connection is tested before enabling the chat interface</li>
          </ul>
        </div>
      </div>
    </div>
  );
};
