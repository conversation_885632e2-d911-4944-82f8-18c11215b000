export interface ChatMessage {
  id: string;
  text: string;
  isBot: boolean;
  timestamp: Date;
}

export interface APIResponse {
  success: boolean;
  message: string;
  error?: string;
}

export class MicrochipAPI {
  private apiKey: string = '';
  private conversationHistory: string[] = [];
  private readonly baseURL = 'https://ai-apps.microchip.com';
  private readonly endpoint = '/CodeGPTAPI/api/Chat/CodeCompletion';

  setApiKey(apiKey: string): void {
    this.apiKey = apiKey.trim();
  }

  getApiKey(): string {
    return this.apiKey;
  }

  clearHistory(): void {
    this.conversationHistory = [];
  }

  async testConnection(): Promise<APIResponse> {
    if (!this.apiKey) {
      return {
        success: false,
        message: '',
        error: 'API key is required'
      };
    }

    try {
      const response = await this.callAPI('Hello, are you working?');
      return {
        success: true,
        message: response
      };
    } catch (error) {
      return {
        success: false,
        message: '',
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }

  async sendMessage(userMessage: string): Promise<string> {
    if (!this.apiKey) {
      throw new Error('API key is required');
    }

    if (!userMessage.trim()) {
      throw new Error('Message cannot be empty');
    }

    try {
      const response = await this.callAPI(userMessage);
      this.conversationHistory.push(userMessage);
      
      // Keep only last 5 messages for context
      if (this.conversationHistory.length > 5) {
        this.conversationHistory = this.conversationHistory.slice(-5);
      }
      
      return response;
    } catch (error) {
      throw error;
    }
  }

  private async callAPI(userMessage: string): Promise<string> {
    const requestBody = {
      questions: [userMessage],
      answers: this.conversationHistory.slice(-5),
      category: 101,
      logQnA: true,
      client: "react-chatbot"
    };

    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 30000); // 30 second timeout

    try {
      const response = await fetch(`${this.baseURL}${this.endpoint}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json; charset=utf-8',
          'api-key': this.apiKey,
          'User-Agent': 'Microchip-Chatbot-React/1.0'
        },
        body: JSON.stringify(requestBody),
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        if (response.status === 401) {
          throw new Error('Invalid API key or unauthorized access');
        } else if (response.status === 429) {
          throw new Error('Rate limit exceeded. Please wait and try again');
        } else if (response.status >= 500) {
          throw new Error('Server error. Please try again later');
        } else {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
      }

      const data = await response.text();
      
      // Try to parse as JSON first
      try {
        const jsonResponse = JSON.parse(data);
        
        // Handle different possible response formats
        if (jsonResponse.response) {
          return jsonResponse.response;
        } else if (jsonResponse.answer) {
          return jsonResponse.answer;
        } else if (jsonResponse.content) {
          return jsonResponse.content;
        } else if (jsonResponse.message) {
          return jsonResponse.message;
        } else if (jsonResponse.result) {
          return jsonResponse.result;
        } else if (typeof jsonResponse === 'string') {
          return jsonResponse;
        } else if (Array.isArray(jsonResponse) && jsonResponse.length > 0) {
          return jsonResponse[0].response || jsonResponse[0].answer || JSON.stringify(jsonResponse[0]);
        } else {
          return JSON.stringify(jsonResponse);
        }
      } catch (jsonError) {
        // If JSON parsing fails, return the raw text
        return data.trim() || 'I received an empty response from the API.';
      }

    } catch (error) {
      clearTimeout(timeoutId);
      
      if (error instanceof Error) {
        if (error.name === 'AbortError') {
          throw new Error('Request timeout - API took too long to respond');
        } else if (error.message.includes('Failed to fetch')) {
          throw new Error('Network error - Please check your internet connection');
        } else {
          throw error;
        }
      } else {
        throw new Error('An unexpected error occurred');
      }
    }
  }
}

// Create a singleton instance
export const microchipAPI = new MicrochipAPI();
