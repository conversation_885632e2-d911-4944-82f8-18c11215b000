import { useState } from 'react'
import { ApiKeyInput } from './components/ApiKeyInput'
import { Chatbot } from './components/Chatbot'
import './App.css'

function App() {
  const [isApiKeySet, setIsApiKeySet] = useState(false)

  const handleApiKeySet = (isValid: boolean) => {
    setIsApiKeySet(isValid)
  }

  const handleDisconnect = () => {
    setIsApiKeySet(false)
  }

  return (
    <div className="app">
      {!isApiKeySet ? (
        <ApiKeyInput onApiKeySet={handleApiKeySet} />
      ) : (
        <Chatbot onDisconnect={handleDisconnect} />
      )}
    </div>
  )
}

export default App
